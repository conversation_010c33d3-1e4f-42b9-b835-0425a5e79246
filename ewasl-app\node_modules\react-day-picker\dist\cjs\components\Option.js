"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Option = Option;
const react_1 = __importDefault(require("react"));
/**
 * Render an `option` element.
 *
 * @group Components
 * @see https://daypicker.dev/guides/custom-components
 */
function Option(props) {
    return react_1.default.createElement("option", { ...props });
}
//# sourceMappingURL=Option.js.map